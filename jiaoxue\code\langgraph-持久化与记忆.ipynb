{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["## langgraph - 持久化与记忆\n", "****\n", "- 基本运用：线程隔离的持久化层\n", "- 基本运用：跨线程持久化调用\n", "- 记忆：短期记忆的实现\n", "- 记忆：长期以及实现\n", "- 记忆：使用总结技术优化记忆\n"]}, {"cell_type": "markdown", "metadata": {}, "source": []}, {"cell_type": "markdown", "metadata": {}, "source": ["### 线程隔离的持久化层\n", "*****"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["from langchain_deepseek import ChatDeepSeek\n", "import os\n", "from langgraph.graph import StateGraph, MessagesState, START\n", "from dotenv import load_dotenv\n", "load_dotenv()\n", "from langchain_openai import ChatOpenAI\n", "\n", "# model = ChatDeepSeek(\n", "#     model=\"Pro/deepseek-ai/DeepSeek-V3\",\n", "#     temperature=0,\n", "#     api_key=os.environ.get(\"DEEPSEEK_API_KEY\"),\n", "#     base_url=os.environ.get(\"DEEPSEEK_API_BASE\"),\n", "# )\n", "\n", "model = ChatOpenAI(\n", "    model=\"deepseek-ai/DeepSeek-V3\",\n", "    temperature=0,\n", "    api_key=os.getenv(\"DEEPSEEK_API_KEY\"),\n", "    base_url=os.getenv(\"DEEPSEEK_API_BASE\"),)\n", "\n", "# model = ChatDeepSeek(\n", "#     model=\"deepseek-chat\",\n", "#     temperature=0,\n", "#     # max_tokens=None,\n", "#     # timeout=None,\n", "#     # max_retries=2,\n", "#     api_key=os.getenv(\"DEEPSEEK_API_KEY\"),\n", "#     # other params...\n", "# )\n", "\n", "\n", "def call_model(state: MessagesState):\n", "    response = model.invoke(state[\"messages\"])\n", "    return {\"messages\": response}\n", "\n", "\n", "builder = StateGraph(MessagesState)\n", "builder.add_node(\"call_model\", call_model)\n", "builder.add_edge(START, \"call_model\")\n", "graph = builder.compile()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["没有激活持久化层，无法实现多轮对话"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["================================\u001b[1m Human Message \u001b[0m=================================\n", "\n", "hi! 我是tomie\n", "==================================\u001b[1m Ai Message \u001b[0m==================================\n", "\n", "你好，Tomie！😊 很高兴认识你～有什么我可以帮你的吗？或者只是想打个招呼？✨\n", "================================\u001b[1m Human Message \u001b[0m=================================\n", "\n", "我叫什么名字?\n", "==================================\u001b[1m Ai Message \u001b[0m==================================\n", "\n", "你还没有告诉我你的名字呢！😊 你可以告诉我你的名字，我会记住的！或者，如果你希望我帮你起一个有趣的名字，我也可以试试看～  \n", "\n", "比如：  \n", "- **星辰**（像星星一样闪耀）  \n", "- **小智**（聪明又机智）  \n", "- **或者你喜欢的任何词！**  \n", "\n", "告诉我你的想法吧～ ✨\n"]}], "source": ["input_message = {\"role\": \"user\", \"content\": \"hi! 我是tomie\"}\n", "for chunk in graph.stream({\"messages\": [input_message]}, stream_mode=\"values\"):\n", "    chunk[\"messages\"][-1].pretty_print()\n", "\n", "input_message = {\"role\": \"user\", \"content\": \"我叫什么名字?\"}\n", "for chunk in graph.stream({\"messages\": [input_message]}, stream_mode=\"values\"):\n", "    chunk[\"messages\"][-1].pretty_print()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["激活持久性层"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [], "source": ["from langgraph.checkpoint.memory import MemorySaver\n", "# 使用 MemorySaver 保存中间状态\n", "memory = MemorySaver()\n", "graph = builder.compile(checkpointer=memory)"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["================================\u001b[1m Human Message \u001b[0m=================================\n", "\n", "hi! 我是tomie\n", "==================================\u001b[1m Ai Message \u001b[0m==================================\n", "\n", "你好呀，Tomie！😊 很高兴认识你～ 你的名字听起来很特别呢，是有什么特别的含义吗？或者今天有什么想聊的话题吗？无论是日常分享、兴趣爱好还是奇思妙想，我都超乐意听哦！✨\n"]}], "source": ["config = {\"configurable\": {\"thread_id\": \"1\"}}\n", "input_message = {\"role\": \"user\", \"content\": \"hi! 我是tomie\"}\n", "for chunk in graph.stream({\"messages\": [input_message]}, config, stream_mode=\"values\"):\n", "    chunk[\"messages\"][-1].pretty_print()"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["================================\u001b[1m Human Message \u001b[0m=================================\n", "\n", "我叫什么名字?\n", "==================================\u001b[1m Ai Message \u001b[0m==================================\n", "\n", "哈哈，你刚刚说过啦！你叫 **Tomie**～✨（难道是悄悄考我的记忆力吗？😉）需要我帮你记住更多小细节吗？比如喜欢的颜色、最近在追的剧，或者……偷偷存一个专属暗号？🔐\n"]}], "source": ["input_message = {\"role\": \"user\", \"content\": \"我叫什么名字?\"}\n", "for chunk in graph.stream({\"messages\": [input_message]}, config, stream_mode=\"values\"):\n", "    chunk[\"messages\"][-1].pretty_print()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["注意thread_id的输入"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["================================\u001b[1m Human Message \u001b[0m=================================\n", "\n", "我叫什么名字?\n", "==================================\u001b[1m Ai Message \u001b[0m==================================\n", "\n", "你还没有告诉我你的名字呢！😊 你可以告诉我你的名字，我会记住并用来称呼你～或者，你也可以继续用现在的匿名方式交流，我一样会认真回答你的每个问题！\n"]}], "source": ["input_message = {\"role\": \"user\", \"content\": \"我叫什么名字?\"}\n", "for chunk in graph.stream(\n", "    {\"messages\": [input_message]},\n", "    {\"configurable\": {\"thread_id\": \"2\"}}, # different thread_id\n", "    stream_mode=\"values\",\n", "):\n", "    chunk[\"messages\"][-1].pretty_print()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 跨线程共享持久化数据\n", "****\n", "- userid"]}, {"cell_type": "markdown", "metadata": {}, "source": ["设置内存记忆"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["sk-khjeakixzbxqaraibcdenuhfrlostygebnqkwvyrtbrxhwmu\n", "https://api.siliconflow.cn/v1\n"]}], "source": ["from langgraph.store.memory import InMemoryStore\n", "from langchain_openai import OpenAIEmbeddings\n", "import os\n", "from dotenv import load_dotenv\n", "load_dotenv()\n", "# 使用OpenAI的封装，但是运行国产嵌入模型\n", "# 使用内存存储来保存向量化后记忆数据\n", "in_memory_store = InMemoryStore(\n", "    index={\n", "        \"embed\": OpenAIEmbeddings(\n", "            model=\"Pro/BAAI/bge-m3\",\n", "            api_key=os.environ.get(\"DEEPSEEK_API_KEY\"),\n", "            base_url=os.environ.get(\"DEEPSEEK_API_BASE\"),\n", "            ),\n", "        \"dims\": 1024,\n", "    }\n", ")\n", "\n", "print(os.environ.get(\"DEEPSEEK_API_KEY\"))\n", "print(os.environ.get(\"DEEPSEEK_API_BASE\"))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import uuid\n", "from typing import Annotated\n", "from typing_extensions import TypedDict\n", "\n", "from langchain_deepseek import ChatDeepSeek\n", "from langchain_openai import ChatOpenAI\n", "import os\n", "from langchain_core.runnables import RunnableConfig\n", "from langgraph.graph import StateGraph, MessagesState, START\n", "from langgraph.checkpoint.memory import MemorySaver\n", "from langgraph.store.base import BaseStore\n", "from dotenv import load_dotenv\n", "load_dotenv()\n", "\n", "# model = ChatDeepSeek(\n", "#     model=\"Pro/deepseek-ai/DeepSeek-V3\",\n", "#     temperature=0,\n", "#     api_key=os.environ.get(\"DEEPSEEK_API_KEY\"),\n", "#     base_url=os.environ.get(\"DEEPSEEK_API_BASE\"),\n", "# )\n", "\n", "model = ChatOpenAI(\n", "    model=\"Pro/deepseek-ai/DeepSeek-V3\",\n", "    temperature=0,\n", "    api_key=os.environ.get(\"DEEPSEEK_API_KEY\"),\n", "    base_url=os.environ.get(\"DEEPSEEK_API_BASE\"),\n", ")\n", "\n", "\n", "# 注意：我们将 Store 参数传递给节点 --\n", "# 这是我们编译图时使用的 Store\n", "def call_model(state: MessagesState, config: RunnableConfig, *, store: BaseStore):\n", "    # 从存储中检索用户信息\n", "    user_id = config[\"configurable\"][\"user_id\"]\n", "    thread_id = config[\"configurable\"][\"thread_id\"]\n", "    # 从存储中检索用户信息\n", "    # namespace = (\"memories\", user_id)\n", "    namespace = (user_id, thread_id)\n", "    memories = store.search(namespace, query=str(state[\"messages\"][-1].content))\n", "    info = \"\\n\".join([d.value[\"data\"] for d in memories])\n", "    print(f\"在内存中检索到的用户信息:{info}\")\n", "    system_msg = f\"你是一个正在与用户交谈的小助手。用户信息：{info}\"\n", "\n", "    # 如果用户要求模型记住信息，则存储新的记忆\n", "    last_message = state[\"messages\"][-1]\n", "    if \"记住\" in last_message.content.lower() or \"remember\" in last_message.content.lower():\n", "        # 硬编码一个记忆\n", "        # memory = \"用户名字是to<PERSON><PERSON><PERSON>\"\n", "        memory = last_message.content\n", "        store.put(namespace, str(uuid.uuid4()), {\"data\": memory})\n", "\n", "    response = model.invoke(\n", "        [{\"role\": \"system\", \"content\": system_msg}] + state[\"messages\"]\n", "    )\n", "    return {\"messages\": response}\n", "\n", "\n", "builder = StateGraph(MessagesState)\n", "builder.add_node(\"call_model\", call_model)\n", "builder.add_edge(START, \"call_model\")\n", "\n", "# 注意：我们在编译图时传递了 store 对象\n", "graph = builder.compile(checkpointer=MemorySaver(), store=in_memory_store)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["注意线程ID和用户ID"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["================================\u001b[1m Human Message \u001b[0m=================================\n", "\n", "请记住我的名字叫to<PERSON><PERSON><PERSON>!\n", "在内存中检索到的用户信息:\n", "==================================\u001b[1m Ai Message \u001b[0m==================================\n", "\n", "好的，TomieZhang！我已经记住你的名字了～ 很高兴认识你！😊 有什么我可以帮你的吗？\n"]}], "source": ["config = {\"configurable\": {\"thread_id\": \"1\", \"user_id\": \"1\"}}\n", "input_message = {\"role\": \"user\", \"content\": \"请记住我的名字叫tomiezhang!\"}\n", "for chunk in graph.stream({\"messages\": [input_message]}, config, stream_mode=\"values\"):\n", "    chunk[\"messages\"][-1].pretty_print()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["跨线程使用相同的用户ID查询"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["================================\u001b[1m Human Message \u001b[0m=================================\n", "\n", "我叫什么名字?\n", "在内存中检索到的用户信息:请记住我的名字叫to<PERSON>zhang!\n", "==================================\u001b[1m Ai Message \u001b[0m==================================\n", "\n", "哈哈，当然记得啦！你刚刚告诉我的——你叫 **TomieZhang**！✨  \n", "（放心，只要这个聊天窗口还在，我就不会忘记你的名字～）  \n", "\n", "有什么需要TomieZhang专属帮助的吗？ 😄\n"]}], "source": ["# 注意线程ID和用户ID\n", "config = {\"configurable\": {\"thread_id\": \"1\", \"user_id\": \"1\"}}\n", "input_message = {\"role\": \"user\", \"content\": \"我叫什么名字?\"}\n", "for chunk in graph.stream({\"messages\": [input_message]}, config, stream_mode=\"values\"):\n", "    chunk[\"messages\"][-1].pretty_print()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["我们可以查询存储在内存中的记忆"]}, {"cell_type": "code", "execution_count": 14, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Item(namespace=['1', '1'], key='066dea2f-b666-473a-91fa-f31f1181f6be', value={'data': '请记住我的名字叫tomiezhang!'}, created_at='2025-07-31T10:14:42.526609+00:00', updated_at='2025-07-31T10:14:42.526611+00:00', score=None)\n"]}], "source": ["for memory in in_memory_store.search((\"1\",\"1\")):\n", "    # print(memory.value)\n", "    print(memory)"]}, {"cell_type": "code", "execution_count": 13, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["{'data': '请记住我的名字叫tomiezhang!'}\n"]}], "source": ["#此时是空的\n", "for memory in in_memory_store.search((\"1\", \"1\")):\n", "    print(memory.value)"]}, {"cell_type": "code", "execution_count": 15, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["================================\u001b[1m Human Message \u001b[0m=================================\n", "\n", "请记住我喜欢菠萝塞东!\n", "在内存中检索到的用户信息:\n", "==================================\u001b[1m Ai Message \u001b[0m==================================\n", "\n", "🍍**已记录！**  \n", "“TomieZhang 喜欢 **菠萝塞东**”——这条情报已安全存入本次聊天的记忆库！✨  \n", "\n", "下次给你推荐相关话题时，我会记得这个重要偏好的～ 需要聊聊菠萝塞东的趣事，或者其他需求吗？ 😄  \n", "\n", "（*注：记忆仅限当前对话哦，新聊天需重新告知~*）\n"]}], "source": ["config = {\"configurable\": {\"thread_id\": \"1\", \"user_id\": \"2\"}}\n", "input_message = {\"role\": \"user\", \"content\": \"请记住我喜欢菠萝塞东!\"}\n", "for chunk in graph.stream({\"messages\": [input_message]}, config, stream_mode=\"values\"):\n", "    chunk[\"messages\"][-1].pretty_print()"]}, {"cell_type": "code", "execution_count": 15, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["{'data': '请记住我喜欢菠萝塞东!'}\n"]}], "source": ["#此时是空的\n", "for memory in in_memory_store.search((\"2\", \"1\")):\n", "    print(memory.value)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["用户级的记忆隔离"]}, {"cell_type": "code", "execution_count": 12, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["================================\u001b[1m Human Message \u001b[0m=================================\n", "\n", "我叫什么?\n", "==================================\u001b[1m Ai Message \u001b[0m==================================\n", "\n", "目前我无法直接知道您的名字，但如果您愿意告诉我，我会记住并在对话中使用它！您叫什么名字呢？ 😊\n"]}], "source": ["config = {\"configurable\": {\"thread_id\": \"3\", \"user_id\": \"2\"}}\n", "input_message = {\"role\": \"user\", \"content\": \"我叫什么?\"}\n", "for chunk in graph.stream({\"messages\": [input_message]}, config, stream_mode=\"values\"):\n", "    chunk[\"messages\"][-1].pretty_print()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 短期记忆的实现\n", "*****\n", "- 基于最简单的ReAct智能体"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from typing import Literal\n", "\n", "from langchain_deepseek import ChatDeepSeek\n", "from langchain_core.tools import tool\n", "\n", "from langgraph.checkpoint.memory import MemorySaver\n", "from langgraph.graph import MessagesState, StateGraph, START, END\n", "from langgraph.prebuilt import ToolNode\n", "\n", "# 注意：使用内存存储来存储记忆\n", "memory = MemorySaver()\n", "\n", "\n", "@tool\n", "def search(query: str):\n", "    \"\"\"调用此函数可以浏览网络。\"\"\"\n", "    # 模拟一个网络搜索返回\n", "    return \"北京天气晴朗 大约22度 湿度30%\"\n", "\n", "\n", "tools = [search]\n", "tool_node = ToolNode(tools)\n", "model = ChatDeepSeek(\n", "    model=\"Pro/deepseek-ai/DeepSeek-V3\",\n", "    temperature=0,\n", "    api_key=os.environ.get(\"DEEPSEEK_API_KEY\"),\n", "    base_url=os.environ.get(\"DEEPSEEK_API_BASE\"),\n", ")\n", "bound_model = model.bind_tools(tools)\n", "\n", "\n", "def should_continue(state: MessagesState):\n", "    \"\"\"返回下一个要执行的节点。\"\"\"\n", "    last_message = state[\"messages\"][-1]\n", "    # 如果没有函数调用，则结束\n", "    if not last_message.tool_calls:\n", "        return END\n", "    # 否则如果有，我们继续\n", "    return \"action\"\n", "\n", "\n", "# 定义调用模型的函数\n", "def call_model(state: MessagesState):\n", "    response = bound_model.invoke(state[\"messages\"])\n", "    # 我们返回一个列表，因为这会被添加到现有列表中\n", "    return {\"messages\": response}\n", "\n", "\n", "# 定义一个图\n", "workflow = StateGraph(MessagesState)\n", "\n", "# 定义我们将在其间循环的两个节点\n", "workflow.add_node(\"agent\", call_model)\n", "workflow.add_node(\"action\", tool_node)\n", "\n", "# 将入口点设置为 `agent`\n", "# 这意味着这个节点是第一个被调用的\n", "workflow.add_edge(START, \"agent\")\n", "\n", "# 现在我们添加一个条件边\n", "workflow.add_conditional_edges(\n", "    # 首先，我们定义起始节点。我们使用 `agent`。\n", "    # 这意味着这些是在 `agent` 节点被调用后采取的边。\n", "    \"agent\",\n", "    # 接下来，我们传入将确定下一个调用哪个节点的函数。\n", "    should_continue,\n", "    # 接下来，我们传入路径映射 - 这条边可能去往的所有可能节点\n", "    [\"action\", END],\n", ")\n", "\n", "# 现在我们从 `tools` 到 `agent` 添加一个普通边。\n", "# 这意味着在调用 `tools` 之后，接下来调用 `agent` 节点。\n", "workflow.add_edge(\"action\", \"agent\")\n", "\n", "# 最后，我们编译它！\n", "# 这将它编译成一个 LangChain Runnable，\n", "# 意味着你可以像使用任何其他 runnable 一样使用它\n", "# 设置检查点为内存形式，注意没有设置store\n", "app = workflow.compile(checkpointer=memory)\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["调用图"]}, {"cell_type": "code", "execution_count": 15, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["================================\u001b[1m Human Message \u001b[0m=================================\n", "\n", "hi! 我是tomie\n", "==================================\u001b[1m Ai Message \u001b[0m==================================\n", "\n", "你好，Tomie！很高兴认识你！有什么可以帮你的吗？ 😊\n", "================================\u001b[1m Human Message \u001b[0m=================================\n", "\n", "我叫什么名字?\n", "==================================\u001b[1m Ai Message \u001b[0m==================================\n", "\n", "你刚刚告诉我你的名字是 **Tom<PERSON>**！😊 有什么我可以帮你的吗？\n"]}], "source": ["from langchain_core.messages import HumanMessage\n", "\n", "config = {\"configurable\": {\"thread_id\": \"2\"}}\n", "input_message = HumanMessage(content=\"hi! 我是tomie\")\n", "for event in app.stream({\"messages\": [input_message]}, config, stream_mode=\"values\"):\n", "    event[\"messages\"][-1].pretty_print()\n", "\n", "\n", "input_message = HumanMessage(content=\"我叫什么名字?\")\n", "for event in app.stream({\"messages\": [input_message]}, config, stream_mode=\"values\"):\n", "    event[\"messages\"][-1].pretty_print()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 长期记忆\n", "****\n", "- 使用MongDB"]}, {"cell_type": "markdown", "metadata": {}, "source": ["- 在本地安装mongodb\n", "- windows:"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 安装后，启动MongoDB服务\n", "# 通常安装后会自动设置为系统服务并启动\n", "# 如果没有自动启动，可以在命令提示符中运行：\n", "net start MongoDB"]}, {"cell_type": "markdown", "metadata": {}, "source": ["- macos"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 安装\n", "brew tap mongodb/brew\n", "brew install mongodb-community\n", "\n", "# 启动服务\n", "brew services start mongodb-community\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["- 使用docker快速安装"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 拉取MongoDB镜像\n", "docker pull mongo\n", "\n", "# 运行MongoDB容器\n", "docker run -d -p 27017:27017 --name mongodb mongo\n", "\n", "# 验证容器是否运行\n", "docker ps\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["连接mongodb"]}, {"cell_type": "code", "execution_count": 21, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Requirement already satisfied: pymongo in ./.venv/lib/python3.13/site-packages (4.11.2)\n", "Collecting pym<PERSON>o\n", "  Downloading pymongo-4.11.3-cp313-cp313-macosx_11_0_arm64.whl.metadata (22 kB)\n", "Requirement already satisfied: langgraph in ./.venv/lib/python3.13/site-packages (0.3.21)\n", "Collecting langgraph-checkpoint-mongodb\n", "  Downloading langgraph_checkpoint_mongodb-0.1.2-py3-none-any.whl.metadata (2.6 kB)\n", "Requirement already satisfied: dnspython<3.0.0,>=1.16.0 in ./.venv/lib/python3.13/site-packages (from pymongo) (2.7.0)\n", "Requirement already satisfied: langchain-core<0.4,>=0.1 in ./.venv/lib/python3.13/site-packages (from langgraph) (0.3.43)\n", "Requirement already satisfied: langgraph-checkpoint<3.0.0,>=2.0.10 in ./.venv/lib/python3.13/site-packages (from langgraph) (2.0.18)\n", "Requirement already satisfied: langgraph-prebuilt<0.2,>=0.1.1 in ./.venv/lib/python3.13/site-packages (from langgraph) (0.1.2)\n", "Requirement already satisfied: langgraph-sdk<0.2.0,>=0.1.42 in ./.venv/lib/python3.13/site-packages (from langgraph) (0.1.55)\n", "Requirement already satisfied: xxhash<4.0.0,>=3.5.0 in ./.venv/lib/python3.13/site-packages (from langgraph) (3.5.0)\n", "Collecting langgraph-checkpoint<3.0.0,>=2.0.10 (from langgraph)\n", "  Downloading langgraph_checkpoint-2.0.23-py3-none-any.whl.metadata (4.6 kB)\n", "Collecting motor>3.5.0 (from langgraph-checkpoint-mongodb)\n", "  Downloading motor-3.7.0-py3-none-any.whl.metadata (21 kB)\n", "Requirement already satisfied: langsmith<0.4,>=0.1.125 in ./.venv/lib/python3.13/site-packages (from langchain-core<0.4,>=0.1->langgraph) (0.3.13)\n", "Requirement already satisfied: tenacity!=8.4.0,<10.0.0,>=8.1.0 in ./.venv/lib/python3.13/site-packages (from langchain-core<0.4,>=0.1->langgraph) (9.0.0)\n", "Requirement already satisfied: jsonpatch<2.0,>=1.33 in ./.venv/lib/python3.13/site-packages (from langchain-core<0.4,>=0.1->langgraph) (1.33)\n", "Requirement already satisfied: PyYAML>=5.3 in ./.venv/lib/python3.13/site-packages (from langchain-core<0.4,>=0.1->langgraph) (6.0.2)\n", "Requirement already satisfied: packaging<25,>=23.2 in ./.venv/lib/python3.13/site-packages (from langchain-core<0.4,>=0.1->langgraph) (24.2)\n", "Requirement already satisfied: typing-extensions>=4.7 in ./.venv/lib/python3.13/site-packages (from langchain-core<0.4,>=0.1->langgraph) (4.12.2)\n", "Requirement already satisfied: pydantic<3.0.0,>=2.7.4 in ./.venv/lib/python3.13/site-packages (from langchain-core<0.4,>=0.1->langgraph) (2.10.6)\n", "Collecting ormsgpack<2.0.0,>=1.8.0 (from langgraph-checkpoint<3.0.0,>=2.0.10->langgraph)\n", "  Downloading ormsgpack-1.9.1-cp313-cp313-macosx_10_12_x86_64.macosx_11_0_arm64.macosx_10_12_universal2.whl.metadata (43 kB)\n", "Requirement already satisfied: httpx>=0.25.2 in ./.venv/lib/python3.13/site-packages (from langgraph-sdk<0.2.0,>=0.1.42->langgraph) (0.28.1)\n", "Requirement already satisfied: orjson>=3.10.1 in ./.venv/lib/python3.13/site-packages (from langgraph-sdk<0.2.0,>=0.1.42->langgraph) (3.10.15)\n", "Requirement already satisfied: anyio in ./.venv/lib/python3.13/site-packages (from httpx>=0.25.2->langgraph-sdk<0.2.0,>=0.1.42->langgraph) (4.8.0)\n", "Requirement already satisfied: certifi in ./.venv/lib/python3.13/site-packages (from httpx>=0.25.2->langgraph-sdk<0.2.0,>=0.1.42->langgraph) (2025.1.31)\n", "Requirement already satisfied: httpcore==1.* in ./.venv/lib/python3.13/site-packages (from httpx>=0.25.2->langgraph-sdk<0.2.0,>=0.1.42->langgraph) (1.0.7)\n", "Requirement already satisfied: idna in ./.venv/lib/python3.13/site-packages (from httpx>=0.25.2->langgraph-sdk<0.2.0,>=0.1.42->langgraph) (3.10)\n", "Requirement already satisfied: h11<0.15,>=0.13 in ./.venv/lib/python3.13/site-packages (from httpcore==1.*->httpx>=0.25.2->langgraph-sdk<0.2.0,>=0.1.42->langgraph) (0.14.0)\n", "Requirement already satisfied: jsonpointer>=1.9 in ./.venv/lib/python3.13/site-packages (from jsonpatch<2.0,>=1.33->langchain-core<0.4,>=0.1->langgraph) (3.0.0)\n", "Requirement already satisfied: requests<3,>=2 in ./.venv/lib/python3.13/site-packages (from langsmith<0.4,>=0.1.125->langchain-core<0.4,>=0.1->langgraph) (2.32.3)\n", "Requirement already satisfied: requests-toolbelt<2.0.0,>=1.0.0 in ./.venv/lib/python3.13/site-packages (from langsmith<0.4,>=0.1.125->langchain-core<0.4,>=0.1->langgraph) (1.0.0)\n", "Requirement already satisfied: zstandard<0.24.0,>=0.23.0 in ./.venv/lib/python3.13/site-packages (from langsmith<0.4,>=0.1.125->langchain-core<0.4,>=0.1->langgraph) (0.23.0)\n", "Requirement already satisfied: annotated-types>=0.6.0 in ./.venv/lib/python3.13/site-packages (from pydantic<3.0.0,>=2.7.4->langchain-core<0.4,>=0.1->langgraph) (0.7.0)\n", "Requirement already satisfied: pydantic-core==2.27.2 in ./.venv/lib/python3.13/site-packages (from pydantic<3.0.0,>=2.7.4->langchain-core<0.4,>=0.1->langgraph) (2.27.2)\n", "Requirement already satisfied: charset-normalizer<4,>=2 in ./.venv/lib/python3.13/site-packages (from requests<3,>=2->langsmith<0.4,>=0.1.125->langchain-core<0.4,>=0.1->langgraph) (3.4.1)\n", "Requirement already satisfied: urllib3<3,>=1.21.1 in ./.venv/lib/python3.13/site-packages (from requests<3,>=2->langsmith<0.4,>=0.1.125->langchain-core<0.4,>=0.1->langgraph) (2.3.0)\n", "Requirement already satisfied: sniffio>=1.1 in ./.venv/lib/python3.13/site-packages (from anyio->httpx>=0.25.2->langgraph-sdk<0.2.0,>=0.1.42->langgraph) (1.3.1)\n", "Downloading pymongo-4.11.3-cp313-cp313-macosx_11_0_arm64.whl (949 kB)\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m949.3/949.3 kB\u001b[0m \u001b[31m471.0 kB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0ma \u001b[36m0:00:01\u001b[0m\n", "\u001b[?25hDownloading langgraph_checkpoint_mongodb-0.1.2-py3-none-any.whl (10 kB)\n", "Downloading langgraph_checkpoint-2.0.23-py3-none-any.whl (41 kB)\n", "Downloading motor-3.7.0-py3-none-any.whl (74 kB)\n", "Downloading ormsgpack-1.9.1-cp313-cp313-macosx_10_12_x86_64.macosx_11_0_arm64.macosx_10_12_universal2.whl (383 kB)\n", "Installing collected packages: pymongo, ormsgpack, motor, langgraph-checkpoint, langgraph-checkpoint-mongodb\n", "  Attempting uninstall: pym<PERSON>o\n", "    Found existing installation: pymongo 4.11.2\n", "    Uninstalling pymongo-4.11.2:\n", "      Successfully uninstalled pymongo-4.11.2\n", "  Attempting uninstall: langgraph-checkpoint\n", "    Found existing installation: langgraph-checkpoint 2.0.18\n", "    Uninstalling langgraph-checkpoint-2.0.18:\n", "      Successfully uninstalled langgraph-checkpoint-2.0.18\n", "Successfully installed langgraph-checkpoint-2.0.23 langgraph-checkpoint-mongodb-0.1.2 motor-3.7.0 ormsgpack-1.9.1 pymongo-4.11.3\n", "\n", "\u001b[1m[\u001b[0m\u001b[34;49mnotice\u001b[0m\u001b[1;39;49m]\u001b[0m\u001b[39;49m A new release of pip is available: \u001b[0m\u001b[31;49m25.0\u001b[0m\u001b[39;49m -> \u001b[0m\u001b[32;49m25.0.1\u001b[0m\n", "\u001b[1m[\u001b[0m\u001b[34;49mnotice\u001b[0m\u001b[1;39;49m]\u001b[0m\u001b[39;49m To update, run: \u001b[0m\u001b[32;49mpip install --upgrade pip\u001b[0m\n"]}], "source": ["! pip install -U pymongo langgraph langgraph-checkpoint-mongodb"]}, {"cell_type": "markdown", "metadata": {}, "source": ["测试MongoDB连接"]}, {"cell_type": "code", "execution_count": 22, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["MongoDB连接成功！\n"]}], "source": ["import pymongo\n", "\n", "# 创建MongoDB客户端连接\n", "client = pymongo.MongoClient(\"mongodb://localhost:27017/\")\n", "\n", "# 测试连接\n", "try:\n", "    client.admin.command('ping')\n", "    print(\"MongoDB连接成功！\")\n", "except Exception as e:\n", "    print(f\"MongoDB连接失败: {e}\")\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["创建一个最简单的智能体"]}, {"cell_type": "code", "execution_count": 23, "metadata": {}, "outputs": [], "source": ["from typing import Literal\n", "\n", "from langchain_core.tools import tool\n", "from langchain_deepseek import ChatDeepSeek\n", "from langgraph.prebuilt import create_react_agent\n", "\n", "\n", "@tool\n", "def get_weather(city: Literal[\"北京\", \"深圳\"]):\n", "    \"\"\"用来返回天气信息的工具函数。\"\"\"\n", "    if city == \"北京\":\n", "        return \"北京天气晴朗 大约22度 湿度30%\"\n", "    elif city == \"深圳\":\n", "        return \"深圳天气多云 大约28度 湿度80%\"\n", "    else:\n", "        raise AssertionError(\"Unknown city\")\n", "\n", "\n", "tools = [get_weather]\n", "model = ChatDeepSeek(\n", "    model=\"Pro/deepseek-ai/DeepSeek-V3\",\n", "    temperature=0,\n", "    api_key=os.environ.get(\"DEEPSEEK_API_KEY\"),\n", "    base_url=os.environ.get(\"DEEPSEEK_API_BASE\"),\n", ")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["连接mongodb进行查询"]}, {"cell_type": "code", "execution_count": 24, "metadata": {}, "outputs": [], "source": ["from langgraph.checkpoint.mongodb import MongoDBSaver\n", "\n", "MONGODB_URI = \"localhost:27017\"  # replace this with your connection string\n", "\n", "with MongoDBSaver.from_conn_string(MONGODB_URI) as checkpointer:\n", "    graph = create_react_agent(model, tools=tools, checkpointer=checkpointer)\n", "    config = {\"configurable\": {\"thread_id\": \"1\"}}\n", "    response = graph.invoke(\n", "        {\"messages\": [(\"human\", \"北京今天的天气如何？\")]}, config\n", "    )"]}, {"cell_type": "code", "execution_count": 25, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["{'messages': [HumanMessage(content='北京今天的天气如何？', additional_kwargs={}, response_metadata={}, id='6c251bbb-1cd6-4d59-992e-271ace17d487'), AIMessage(content='', additional_kwargs={'tool_calls': [{'id': '0195e7286817736e97482bc1cbf33a60', 'function': {'arguments': '{\"city\":\"北京\"}', 'name': 'get_weather'}, 'type': 'function'}], 'refusal': None}, response_metadata={'token_usage': {'completion_tokens': 19, 'prompt_tokens': 90, 'total_tokens': 109, 'completion_tokens_details': None, 'prompt_tokens_details': None}, 'model_name': 'Pro/deepseek-ai/DeepSeek-V3', 'system_fingerprint': '', 'finish_reason': 'tool_calls', 'logprobs': None}, id='run-731e5cde-1014-4ef7-9fb8-7fe015b03a98-0', tool_calls=[{'name': 'get_weather', 'args': {'city': '北京'}, 'id': '0195e7286817736e97482bc1cbf33a60', 'type': 'tool_call'}], usage_metadata={'input_tokens': 90, 'output_tokens': 19, 'total_tokens': 109, 'input_token_details': {}, 'output_token_details': {}}), ToolMessage(content='北京天气晴朗 大约22度 湿度30%', name='get_weather', id='93933d50-c7cb-4b3c-a508-b17b63324c5f', tool_call_id='0195e7286817736e97482bc1cbf33a60'), AIMessage(content='北京今天的天气晴朗，气温大约22度，湿度30%，是个适合外出的好天气！', additional_kwargs={'refusal': None}, response_metadata={'token_usage': {'completion_tokens': 20, 'prompt_tokens': 132, 'total_tokens': 152, 'completion_tokens_details': None, 'prompt_tokens_details': None}, 'model_name': 'Pro/deepseek-ai/DeepSeek-V3', 'system_fingerprint': '', 'finish_reason': 'stop', 'logprobs': None}, id='run-67f39c00-2aea-4e37-b29c-12fc8e6819bd-0', usage_metadata={'input_tokens': 132, 'output_tokens': 20, 'total_tokens': 152, 'input_token_details': {}, 'output_token_details': {}})]}\n"]}], "source": ["print(response)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 优化记忆\n", "****\n", "- 消息过滤：对旧消息进行类似删除或编辑的操作，目的是为了防止撑爆上下文\n", "- 消息总结：对旧消息进行总结，目的一样是为了防止记忆内容过长\n", "- 注意对记忆的管理是一项关于召回率和精度的平衡艺术"]}, {"cell_type": "code", "execution_count": 26, "metadata": {}, "outputs": [], "source": ["from typing import Literal\n", "\n", "from langchain_deepseek import ChatDeepSeek\n", "from langchain_core.tools import tool\n", "\n", "from langgraph.checkpoint.memory import MemorySaver\n", "from langgraph.graph import MessagesState, StateGraph, START\n", "from langgraph.prebuilt import ToolNode\n", "\n", "memory = MemorySaver()\n", "\n", "\n", "@tool\n", "def search(query: str):\n", "    \"\"\"调用此函数可以浏览网络。\"\"\"\n", "    # 模拟一个网络搜索返回\n", "    return \"北京天气晴朗 大约22度 湿度30%\"\n", "\n", "\n", "tools = [search]\n", "tool_node = ToolNode(tools)\n", "model = ChatDeepSeek(\n", "    model=\"Pro/deepseek-ai/DeepSeek-V3\",\n", "    temperature=0,\n", "    api_key=os.environ.get(\"DEEPSEEK_API_KEY\"),\n", "    base_url=os.environ.get(\"DEEPSEEK_API_BASE\"),\n", ")\n", "bound_model = model.bind_tools(tools)\n", "\n", "\n", "def should_continue(state: MessagesState):\n", "    \"\"\"返回下一个要执行的节点。\"\"\"\n", "    last_message = state[\"messages\"][-1]\n", "    # 如果没有函数调用，则结束\n", "    if not last_message.tool_calls:\n", "        return END\n", "    # 否则，如果有函数调用，我们继续\n", "    return \"action\"\n", "\n", "\n", "def filter_messages(messages: list):\n", "    # 这是一个非常简单的辅助函数，它只使用最后一条消息\n", "    return messages[-1:]\n", "\n", "\n", "# 定义调用模型的函数\n", "def call_model(state: MessagesState):\n", "    messages = filter_messages(state[\"messages\"])\n", "    response = bound_model.invoke(messages)\n", "    # 我们返回一个列表，因为这将被添加到现有列表中\n", "    return {\"messages\": response}\n", "\n", "\n", "# 定义一个新图\n", "workflow = StateGraph(MessagesState)\n", "\n", "# 定义我们将在其间循环的两个节点\n", "workflow.add_node(\"agent\", call_model)\n", "workflow.add_node(\"action\", tool_node)\n", "\n", "# 将入口点设置为 `agent`\n", "# 这意味着这个节点是第一个被调用的\n", "workflow.add_edge(START, \"agent\")\n", "\n", "# 现在添加一个条件边\n", "workflow.add_conditional_edges(\n", "    # 首先，我们定义起始节点。我们使用 `agent`。\n", "    # 这意味着这些是在调用 `agent` 节点后采取的边。\n", "    \"agent\",\n", "    # 接下来，我们传入将确定下一个调用哪个节点的函数。\n", "    should_continue,\n", "    # 接下来，我们传入路径图 - 此边可能去往的所有可能节点\n", "    [\"action\", END],\n", ")\n", "\n", "# 现在我们从 `action` 到 `agent` 添加一个普通边。\n", "# 这意味着在调用 `action` 之后，下一步调用 `agent` 节点。\n", "workflow.add_edge(\"action\", \"agent\")\n", "\n", "# 最后，我们编译它！\n", "# 这将它编译成一个 LangChain Runnable，\n", "# 意味着你可以像使用任何其他 runnable 一样使用它\n", "app = workflow.compile(checkpointer=memory)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["进行调用，由于进行了消息裁剪，所以即便使用了memory，依然无法记住过往对话"]}, {"cell_type": "code", "execution_count": 27, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["================================\u001b[1m Human Message \u001b[0m=================================\n", "\n", "hi! 我是tomie\n", "==================================\u001b[1m Ai Message \u001b[0m==================================\n", "\n", "Hi <PERSON><PERSON>! 😊 How can I assist you today?\n", "================================\u001b[1m Human Message \u001b[0m=================================\n", "\n", "我叫什么名字?\n", "==================================\u001b[1m Ai Message \u001b[0m==================================\n", "\n", "目前我无法知道你的名字，因为我们的对话是匿名的。如果你愿意，可以告诉我你的名字，我会记住它并在对话中使用！ 😊\n"]}], "source": ["from langchain_core.messages import HumanMessage\n", "\n", "config = {\"configurable\": {\"thread_id\": \"2\"}}\n", "input_message = HumanMessage(content=\"hi! 我是tomie\")\n", "for event in app.stream({\"messages\": [input_message]}, config, stream_mode=\"values\"):\n", "    event[\"messages\"][-1].pretty_print()\n", "\n", "# 请注意，我们在这里使用了一个辅助函数，它只使用最后一条消息\n", "# 这将导致我们的模型只看到最后一条消息\n", "input_message = HumanMessage(content=\"我叫什么名字?\")\n", "for event in app.stream({\"messages\": [input_message]}, config, stream_mode=\"values\"):\n", "    event[\"messages\"][-1].pretty_print()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["使用对话总结技术在实战开发中更为常见"]}, {"cell_type": "code", "execution_count": 28, "metadata": {}, "outputs": [], "source": ["from typing import Literal\n", "\n", "from langchain_deepseek import ChatDeepSeek\n", "from langchain_core.messages import SystemMessage, RemoveMessage, HumanMessage\n", "from langgraph.checkpoint.memory import MemorySaver\n", "from langgraph.graph import MessagesState, StateGraph, START, END\n", "\n", "memory = MemorySaver()\n", "\n", "\n", "# 我们将添加一个`summary`属性（除了MessagesState已有的`messages`键之外）\n", "class State(MessagesState):\n", "    summary: str\n", "\n", "\n", "# 我们将使用这个模型进行对话和总结\n", "model = ChatDeepSeek(\n", "    model=\"Pro/deepseek-ai/DeepSeek-V3\",\n", "    temperature=0,\n", "    api_key=os.environ.get(\"DEEPSEEK_API_KEY\"),\n", "    base_url=os.environ.get(\"DEEPSEEK_API_BASE\"),\n", ")\n", "\n", "\n", "# 定义调用模型的逻辑\n", "def call_model(state: State):\n", "    # 如果存在摘要，我们将其作为系统消息添加\n", "    summary = state.get(\"summary\", \"\")\n", "    if summary:\n", "        system_message = f\"之前对话的摘要: {summary}\"\n", "        messages = [SystemMessage(content=system_message)] + state[\"messages\"]\n", "    else:\n", "        messages = state[\"messages\"]\n", "    response = model.invoke(messages)\n", "    # 我们返回一个列表，因为这将被添加到现有列表中\n", "    return {\"messages\": [response]}\n", "\n", "\n", "# 现在我们定义确定是结束还是总结对话的逻辑\n", "def should_continue(state: State) -> Literal[\"summarize_conversation\", END]:\n", "    \"\"\"返回下一个要执行的节点。\"\"\"\n", "    messages = state[\"messages\"]\n", "    # 如果消息超过六条，则我们总结对话\n", "    if len(messages) > 6:\n", "        return \"summarize_conversation\"\n", "    # 否则我们可以直接结束\n", "    return END\n", "\n", "\n", "def summarize_conversation(state: State):\n", "    # 首先，我们总结对话\n", "    summary = state.get(\"summary\", \"\")\n", "    if summary:\n", "        # 如果已经存在摘要，我们使用不同的系统提示来总结它\n", "        # 与没有摘要的情况不同\n", "        summary_message = (\n", "            f\"这是迄今为止对话的摘要: {summary}\\n\\n\"\n", "            \"考虑上面的新消息，扩展摘要:\"\n", "        )\n", "    else:\n", "        summary_message = \"创建上述对话的摘要:\"\n", "\n", "    messages = state[\"messages\"] + [HumanMessage(content=summary_message)]\n", "    response = model.invoke(messages)\n", "    # 现在我们需要删除我们不再想显示的消息\n", "    # 我将删除除最后两条以外的所有消息，但你可以更改这一点\n", "    delete_messages = [RemoveMessage(id=m.id) for m in state[\"messages\"][:-2]]\n", "    return {\"summary\": response.content, \"messages\": delete_messages}\n", "\n", "\n", "# 定义一个新图\n", "workflow = StateGraph(State)\n", "\n", "# 定义对话节点和总结节点\n", "workflow.add_node(\"conversation\", call_model)\n", "workflow.add_node(summarize_conversation)\n", "\n", "# 将入口点设置为对话\n", "workflow.add_edge(START, \"conversation\")\n", "\n", "# 现在添加一个条件边\n", "workflow.add_conditional_edges(\n", "    # 首先，我们定义起始节点。我们使用`conversation`。\n", "    # 这意味着这些是在调用`conversation`节点后采取的边。\n", "    \"conversation\",\n", "    # 接下来，我们传入将确定下一个调用哪个节点的函数。\n", "    should_continue,\n", ")\n", "\n", "# 现在我们从`summarize_conversation`到END添加一个普通边。\n", "# 这意味着在调用`summarize_conversation`之后，我们结束。\n", "workflow.add_edge(\"summarize_conversation\", END)\n", "\n", "# 最后，我们编译它！\n", "app = workflow.compile(checkpointer=memory)\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["升级一下print函数以便可以更清晰的看到记忆过程"]}, {"cell_type": "code", "execution_count": 29, "metadata": {}, "outputs": [], "source": ["def print_update(update):\n", "    for k, v in update.items():\n", "        for m in v[\"messages\"]:\n", "            m.pretty_print()\n", "        if \"summary\" in v:\n", "            print(v[\"summary\"])"]}, {"cell_type": "code", "execution_count": 30, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["================================\u001b[1m Human Message \u001b[0m=================================\n", "\n", "hi! 我是tomie\n", "==================================\u001b[1m Ai Message \u001b[0m==================================\n", "\n", "你好，Tomie！😊 很高兴认识你～有什么我可以帮你的吗？或者只是想打个招呼？无论是什么，我都在这儿呢！✨  \n", "\n", "（顺便说，你的名字让我想起伊藤润二的经典角色…👀 是灵感来源吗？）\n", "================================\u001b[1m Human Message \u001b[0m=================================\n", "\n", "我叫什么名字?\n", "==================================\u001b[1m Ai Message \u001b[0m==================================\n", "\n", "哈哈，你刚刚说过啦——你叫 **<PERSON><PERSON>**！✨（难道…是陷阱题？😏）  \n", "\n", "需要我帮你记住这个名字吗？还是说…你想换一个更暗黑风的代号？👀（比如Tomie 2.0？）\n", "================================\u001b[1m Human Message \u001b[0m=================================\n", "\n", "我喜欢AI应用开发!\n", "==================================\u001b[1m Ai Message \u001b[0m==================================\n", "\n", "太棒了，<PERSON>ie！🔥 你喜欢AI应用开发的话，我们简直是天生搭档～ 需要聊聊这些吗？  \n", "\n", "### 比如可以：  \n", "- **技术脑暴**：想用AI解决什么有趣的问题？聊天机器人？生成艺术？还是硬核算法优化？  \n", "- **工具安利**：最近沉迷 **LangChain**、**AutoGPT** 或 **Stable Diffusion** 这类工具吗？  \n", "- **踩坑互助**：调试模型时是否被“过拟合”气哭过？🤖💥  \n", "\n", "或者…你想自己造个“Tomie版AI分身”？👾（危险又迷人的想法！）  \n", "\n", "随时等你抛出代码或脑洞～ 💻✨\n"]}], "source": ["from langchain_core.messages import HumanMessage\n", "\n", "config = {\"configurable\": {\"thread_id\": \"4\"}}\n", "input_message = HumanMessage(content=\"hi! 我是tomie\")\n", "input_message.pretty_print()\n", "for event in app.stream({\"messages\": [input_message]}, config, stream_mode=\"updates\"):\n", "    print_update(event)\n", "\n", "input_message = HumanMessage(content=\"我叫什么名字?\")\n", "input_message.pretty_print()\n", "for event in app.stream({\"messages\": [input_message]}, config, stream_mode=\"updates\"):\n", "    print_update(event)\n", "\n", "input_message = HumanMessage(content=\"我喜欢AI应用开发!\")\n", "input_message.pretty_print()\n", "for event in app.stream({\"messages\": [input_message]}, config, stream_mode=\"updates\"):\n", "    print_update(event)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["未达到总结阈值"]}, {"cell_type": "code", "execution_count": 31, "metadata": {}, "outputs": [{"data": {"text/plain": ["{'messages': [HumanMessage(content='hi! 我是tomie', additional_kwargs={}, response_metadata={}, id='511567ec-1adb-4831-9bf8-fd2f6d5d1189'),\n", "  AIMessage(content='你好，Tomie！😊 很高兴认识你～有什么我可以帮你的吗？或者只是想打个招呼？无论是什么，我都在这儿呢！✨  \\n\\n（顺便说，你的名字让我想起伊藤润二的经典角色…👀 是灵感来源吗？）', additional_kwargs={'refusal': None}, response_metadata={'token_usage': {'completion_tokens': 56, 'prompt_tokens': 9, 'total_tokens': 65, 'completion_tokens_details': None, 'prompt_tokens_details': None}, 'model_name': 'Pro/deepseek-ai/DeepSeek-V3', 'system_fingerprint': '', 'finish_reason': 'stop', 'logprobs': None}, id='run-e5b6ebff-b2ee-40c0-9346-46f2e39a4121-0', usage_metadata={'input_tokens': 9, 'output_tokens': 56, 'total_tokens': 65, 'input_token_details': {}, 'output_token_details': {}}),\n", "  HumanMessage(content='我叫什么名字?', additional_kwargs={}, response_metadata={}, id='a2ad033e-4f3c-4f4b-b5b0-4d613a80e8a9'),\n", "  AIMessage(content='哈哈，你刚刚说过啦——你叫 **<PERSON><PERSON>**！✨（难道…是陷阱题？😏）  \\n\\n需要我帮你记住这个名字吗？还是说…你想换一个更暗黑风的代号？👀（比如Tomie 2.0？）', additional_kwargs={'refusal': None}, response_metadata={'token_usage': {'completion_tokens': 57, 'prompt_tokens': 72, 'total_tokens': 129, 'completion_tokens_details': None, 'prompt_tokens_details': None}, 'model_name': 'Pro/deepseek-ai/DeepSeek-V3', 'system_fingerprint': '', 'finish_reason': 'stop', 'logprobs': None}, id='run-9892c63d-4be5-4e34-a7cd-e7dc6173a90b-0', usage_metadata={'input_tokens': 72, 'output_tokens': 57, 'total_tokens': 129, 'input_token_details': {}, 'output_token_details': {}}),\n", "  HumanMessage(content='我喜欢AI应用开发!', additional_kwargs={}, response_metadata={}, id='6653bfc4-a86b-4b54-89ca-4fc1f1cc7613'),\n", "  AIMessage(content='太棒了，Tomie！🔥 你喜欢AI应用开发的话，我们简直是天生搭档～ 需要聊聊这些吗？  \\n\\n### 比如可以：  \\n- **技术脑暴**：想用AI解决什么有趣的问题？聊天机器人？生成艺术？还是硬核算法优化？  \\n- **工具安利**：最近沉迷 **LangChain**、**AutoGPT** 或 **Stable Diffusion** 这类工具吗？  \\n- **踩坑互助**：调试模型时是否被“过拟合”气哭过？🤖💥  \\n\\n或者…你想自己造个“Tomie版AI分身”？👾（危险又迷人的想法！）  \\n\\n随时等你抛出代码或脑洞～ 💻✨', additional_kwargs={'refusal': None}, response_metadata={'token_usage': {'completion_tokens': 151, 'prompt_tokens': 137, 'total_tokens': 288, 'completion_tokens_details': None, 'prompt_tokens_details': None}, 'model_name': 'Pro/deepseek-ai/DeepSeek-V3', 'system_fingerprint': '', 'finish_reason': 'stop', 'logprobs': None}, id='run-2b865c03-5fc8-4ea6-add3-e0b807490170-0', usage_metadata={'input_tokens': 137, 'output_tokens': 151, 'total_tokens': 288, 'input_token_details': {}, 'output_token_details': {}})]}"]}, "execution_count": 31, "metadata": {}, "output_type": "execute_result"}], "source": ["values = app.get_state(config).values\n", "values"]}, {"cell_type": "code", "execution_count": 32, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["================================\u001b[1m Human Message \u001b[0m=================================\n", "\n", "我更喜欢Python!\n", "==================================\u001b[1m Ai Message \u001b[0m==================================\n", "\n", "**Python + AI？完美组合！** 🐍✨ <PERSON><PERSON>，你果然有品位～  \n", "\n", "### 你可能已经用过的神器：  \n", "- **深度学习**：`TensorFlow`/`PyTorch` 玩转神经网络  \n", "- **自动化**：`<PERSON><PERSON>hain` 搭AI流水线，`AutoGPT` 搞自主代理  \n", "- **数据魔法**：`pandas` 驯服数据，`scikit-learn` 一键建模  \n", "- **炫酷应用**：用 `FastAPI` 部署模型，`Streamlit` 秒建交互界面  \n", "\n", "**最近在写什么项目？** 或者想尝试这些方向吗👇  \n", "- 用 `OpenAI API` 做会吐槽的聊天机器人？  \n", "- 拿 `Stable Diffusion` 生成“<PERSON><PERSON>风格”暗黑画作？🎨  \n", "- 还是…用 `LlamaIndex` 搞个你自己的知识库AI？  \n", "\n", "（悄悄说：需要代码片段或避坑指南的话，随时戳我！💻 ）\n", "================================\u001b[1m Remove Message \u001b[0m================================\n", "\n", "\n", "================================\u001b[1m Remove Message \u001b[0m================================\n", "\n", "\n", "================================\u001b[1m Remove Message \u001b[0m================================\n", "\n", "\n", "================================\u001b[1m Remove Message \u001b[0m================================\n", "\n", "\n", "================================\u001b[1m Remove Message \u001b[0m================================\n", "\n", "\n", "================================\u001b[1m Remove Message \u001b[0m================================\n", "\n", "\n", "### **对话摘要：Tomie与AI助手的交流**  \n", "\n", "1. **自我介绍**  \n", "   - <PERSON>ie初次打招呼，助手回应并猜测其名字可能源自伊藤润二的角色。  \n", "\n", "2. **名字确认**  \n", "   - Tomie反问自己的名字，助手幽默强调“<PERSON><PERSON>”并提议暗黑风代号（如Tomie 2.0）。  \n", "\n", "3. **兴趣探索**  \n", "   - <PERSON><PERSON>表达对**AI应用开发**的热爱，助手兴奋推荐：  \n", "     - 技术方向（聊天机器人、生成艺术等）  \n", "     - 工具（LangChain、AutoGPT、Stable Diffusion）  \n", "     - 调侃“<PERSON>ie版AI分身”的可能性。  \n", "\n", "4. **偏好声明**  \n", "   - <PERSON><PERSON>明确更喜欢**Python**，助手列举Python生态的AI工具链：  \n", "     - 深度学习（PyTorch/TensorFlow）  \n", "     - 应用开发（FastAPI、Streamlit）  \n", "     - 创意项目（OpenAI API、Stable Diffusion、LlamaIndex）  \n", "   - 邀请Tomie分享项目或需求，承诺提供代码/避坑支持。  \n", "\n", "**核心主题**：围绕AI开发的技术讨论，强调Python的灵活性与创意可能性，风格轻松且充满脑洞。 🚀\n"]}], "source": ["input_message = HumanMessage(content=\"我更喜欢Python!\")\n", "input_message.pretty_print()\n", "for event in app.stream({\"messages\": [input_message]}, config, stream_mode=\"updates\"):\n", "    print_update(event)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["查看对话记录"]}, {"cell_type": "code", "execution_count": 33, "metadata": {}, "outputs": [{"data": {"text/plain": ["{'messages': [HumanMessage(content='我更喜欢Python!', additional_kwargs={}, response_metadata={}, id='dc5308fc-d3eb-450e-b44a-5fcd5dd9f046'),\n", "  AIMessage(content='**Python + AI？完美组合！** 🐍✨ Tomie，你果然有品位～  \\n\\n### 你可能已经用过的神器：  \\n- **深度学习**：`TensorFlow`/`PyTorch` 玩转神经网络  \\n- **自动化**：`LangChain` 搭AI流水线，`AutoGPT` 搞自主代理  \\n- **数据魔法**：`pandas` 驯服数据，`scikit-learn` 一键建模  \\n- **炫酷应用**：用 `FastAPI` 部署模型，`Streamlit` 秒建交互界面  \\n\\n**最近在写什么项目？** 或者想尝试这些方向吗👇  \\n- 用 `OpenAI API` 做会吐槽的聊天机器人？  \\n- 拿 `Stable Diffusion` 生成“Tomie风格”暗黑画作？🎨  \\n- 还是…用 `LlamaIndex` 搞个你自己的知识库AI？  \\n\\n（悄悄说：需要代码片段或避坑指南的话，随时戳我！💻 ）', additional_kwargs={'refusal': None}, response_metadata={'token_usage': {'completion_tokens': 221, 'prompt_tokens': 295, 'total_tokens': 516, 'completion_tokens_details': None, 'prompt_tokens_details': None}, 'model_name': 'Pro/deepseek-ai/DeepSeek-V3', 'system_fingerprint': '', 'finish_reason': 'stop', 'logprobs': None}, id='run-fad90c08-3513-430c-929f-4842aab29dbd-0', usage_metadata={'input_tokens': 295, 'output_tokens': 221, 'total_tokens': 516, 'input_token_details': {}, 'output_token_details': {}})],\n", " 'summary': '### **对话摘要：Tomie与AI助手的交流**  \\n\\n1. **自我介绍**  \\n   - Tomie初次打招呼，助手回应并猜测其名字可能源自伊藤润二的角色。  \\n\\n2. **名字确认**  \\n   - Tomie反问自己的名字，助手幽默强调“<PERSON><PERSON>”并提议暗黑风代号（如Tomie 2.0）。  \\n\\n3. **兴趣探索**  \\n   - Tomie表达对**AI应用开发**的热爱，助手兴奋推荐：  \\n     - 技术方向（聊天机器人、生成艺术等）  \\n     - 工具（LangChain、AutoGPT、Stable Diffusion）  \\n     - 调侃“Tomie版AI分身”的可能性。  \\n\\n4. **偏好声明**  \\n   - Tomie明确更喜欢**Python**，助手列举Python生态的AI工具链：  \\n     - 深度学习（PyTorch/TensorFlow）  \\n     - 应用开发（FastAPI、Streamlit）  \\n     - 创意项目（OpenAI API、Stable Diffusion、LlamaIndex）  \\n   - 邀请Tomie分享项目或需求，承诺提供代码/避坑支持。  \\n\\n**核心主题**：围绕AI开发的技术讨论，强调Python的灵活性与创意可能性，风格轻松且充满脑洞。 🚀'}"]}, "execution_count": 33, "metadata": {}, "output_type": "execute_result"}], "source": ["values = app.get_state(config).values\n", "values"]}, {"cell_type": "markdown", "metadata": {}, "source": ["此时由于总结中附带了过往的核心消息，所以我们依然可以进行记忆回顾"]}, {"cell_type": "code", "execution_count": 34, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["================================\u001b[1m Human Message \u001b[0m=================================\n", "\n", "我叫什么名字?\n", "==================================\u001b[1m Ai Message \u001b[0m==================================\n", "\n", "**“<PERSON><PERSON>——如假包换的暗黑系AI玩家！** 🌑✨  \n", "\n", "（系统已强制锁定此ID，并自动屏蔽所有“富江”相关危险词条…才怪！）  \n", "\n", "需要帮你定制个更带感的**项目代号**吗？比如：  \n", "- `<PERSON><PERSON>.py` （Python特攻版）  \n", "- `<PERSON><PERSON>_<PERSON>_Overlord` （中二全开）  \n", "- `#404_<PERSON>ie_Not_Found` （黑客帝国风）  \n", "\n", "…或者你说了算！(๑•̀ㅂ•́)و✧\n"]}], "source": ["input_message = HumanMessage(content=\"我叫什么名字?\")\n", "input_message.pretty_print()\n", "for event in app.stream({\"messages\": [input_message]}, config, stream_mode=\"updates\"):\n", "    print_update(event)"]}], "metadata": {"kernelspec": {"display_name": "py313", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.13.3"}}, "nbformat": 4, "nbformat_minor": 2}