from langchain_deepseek import ChatDeepSeek
import os
from langgraph.graph import StateGraph, MessagesState, START
from dotenv import load_dotenv
load_dotenv()
from langchain_openai import ChatOpenAI

# model = ChatDeepSeek(
#     model="Pro/deepseek-ai/DeepSeek-V3",
#     temperature=0,
#     api_key=os.environ.get("DEEPSEEK_API_KEY"),
#     base_url=os.environ.get("DEEPSEEK_API_BASE"),
# )

model = ChatOpenAI(
    model="deepseek-ai/DeepSeek-V3",
    temperature=0,
    api_key=os.getenv("DEEPSEEK_API_KEY"),
    base_url=os.getenv("DEEPSEEK_API_BASE"),)

# model = ChatDeepSeek(
#     model="deepseek-chat",
#     temperature=0,
#     # max_tokens=None,
#     # timeout=None,
#     # max_retries=2,
#     api_key=os.getenv("DEEPSEEK_API_KEY"),
#     # other params...
# )


def call_model(state: MessagesState):
    response = model.invoke(state["messages"])
    return {"messages": response}


builder = StateGraph(MessagesState)
builder.add_node("call_model", call_model)
builder.add_edge(START, "call_model")
graph = builder.compile()

input_message = {"role": "user", "content": "hi! 我是tomie"}
for chunk in graph.stream({"messages": [input_message]}, stream_mode="values"):
    chunk["messages"][-1].pretty_print()

input_message = {"role": "user", "content": "我叫什么名字?"}
for chunk in graph.stream({"messages": [input_message]}, stream_mode="values"):
    chunk["messages"][-1].pretty_print()

from langgraph.checkpoint.memory import MemorySaver
# 使用 MemorySaver 保存中间状态
memory = MemorySaver()
graph = builder.compile(checkpointer=memory)

config = {"configurable": {"thread_id": "1"}}
input_message = {"role": "user", "content": "hi! 我是tomie"}
for chunk in graph.stream({"messages": [input_message]}, config, stream_mode="values"):
    chunk["messages"][-1].pretty_print()

input_message = {"role": "user", "content": "我叫什么名字?"}
for chunk in graph.stream({"messages": [input_message]}, config, stream_mode="values"):
    chunk["messages"][-1].pretty_print()

input_message = {"role": "user", "content": "我叫什么名字?"}
for chunk in graph.stream(
    {"messages": [input_message]},
    {"configurable": {"thread_id": "2"}}, # different thread_id
    stream_mode="values",
):
    chunk["messages"][-1].pretty_print()

from langgraph.store.memory import InMemoryStore
from langchain_openai import OpenAIEmbeddings
import os
from dotenv import load_dotenv
load_dotenv()
# 使用OpenAI的封装，但是运行国产嵌入模型
# 使用内存存储来保存向量化后记忆数据
in_memory_store = InMemoryStore(
    index={
        "embed": OpenAIEmbeddings(
            model="Pro/BAAI/bge-m3",
            api_key=os.environ.get("DEEPSEEK_API_KEY"),
            base_url=os.environ.get("DEEPSEEK_API_BASE"),
            ),
        "dims": 1024,
    }
)

print(os.environ.get("DEEPSEEK_API_KEY"))
print(os.environ.get("DEEPSEEK_API_BASE"))

import uuid
from typing import Annotated
from typing_extensions import TypedDict

from langchain_deepseek import ChatDeepSeek
from langchain_openai import ChatOpenAI
import os
from langchain_core.runnables import RunnableConfig
from langgraph.graph import StateGraph, MessagesState, START
from langgraph.checkpoint.memory import MemorySaver
from langgraph.store.base import BaseStore
from dotenv import load_dotenv
load_dotenv()

# model = ChatDeepSeek(
#     model="Pro/deepseek-ai/DeepSeek-V3",
#     temperature=0,
#     api_key=os.environ.get("DEEPSEEK_API_KEY"),
#     base_url=os.environ.get("DEEPSEEK_API_BASE"),
# )

model = ChatOpenAI(
    model="Pro/deepseek-ai/DeepSeek-V3",
    temperature=0,
    api_key=os.environ.get("DEEPSEEK_API_KEY"),
    base_url=os.environ.get("DEEPSEEK_API_BASE"),
)


# 注意：我们将 Store 参数传递给节点 --
# 这是我们编译图时使用的 Store
def call_model(state: MessagesState, config: RunnableConfig, *, store: BaseStore):
    # 从存储中检索用户信息
    user_id = config["configurable"]["user_id"]
    thread_id = config["configurable"]["thread_id"]
    # 从存储中检索用户信息
    # namespace = ("memories", user_id)
    namespace = (user_id, thread_id)
    memories = store.search(namespace, query=str(state["messages"][-1].content))
    info = "\n".join([d.value["data"] for d in memories])
    print(f"在内存中检索到的用户信息:{info}")
    system_msg = f"你是一个正在与用户交谈的小助手。用户信息：{info}"

    # 如果用户要求模型记住信息，则存储新的记忆
    last_message = state["messages"][-1]
    if "记住" in last_message.content.lower() or "remember" in last_message.content.lower():
        # 硬编码一个记忆
        # memory = "用户名字是tomiezhang"
        memory = last_message.content
        store.put(namespace, str(uuid.uuid4()), {"data": memory})

    response = model.invoke(
        [{"role": "system", "content": system_msg}] + state["messages"]
    )
    return {"messages": response}


builder = StateGraph(MessagesState)
builder.add_node("call_model", call_model)
builder.add_edge(START, "call_model")

# 注意：我们在编译图时传递了 store 对象
graph = builder.compile(checkpointer=MemorySaver(), store=in_memory_store)

config = {"configurable": {"thread_id": "1", "user_id": "1"}}
input_message = {"role": "user", "content": "请记住我的名字叫tomiezhang!"}
for chunk in graph.stream({"messages": [input_message]}, config, stream_mode="values"):
    chunk["messages"][-1].pretty_print()

# 注意线程ID和用户ID
config = {"configurable": {"thread_id": "1", "user_id": "1"}}
input_message = {"role": "user", "content": "我叫什么名字?"}
for chunk in graph.stream({"messages": [input_message]}, config, stream_mode="values"):
    chunk["messages"][-1].pretty_print()

for memory in in_memory_store.search(("1","1")):
    # print(memory.value)
    print(memory)

#此时是空的
for memory in in_memory_store.search(("1", "1")):
    print(memory.value)

config = {"configurable": {"thread_id": "1", "user_id": "2"}}
input_message = {"role": "user", "content": "请记住我喜欢菠萝塞东!"}
for chunk in graph.stream({"messages": [input_message]}, config, stream_mode="values"):
    chunk["messages"][-1].pretty_print()

#此时是空的
for memory in in_memory_store.search(("2", "1")):
    print(memory.value)

config = {"configurable": {"thread_id": "3", "user_id": "2"}}
input_message = {"role": "user", "content": "我叫什么?"}
for chunk in graph.stream({"messages": [input_message]}, config, stream_mode="values"):
    chunk["messages"][-1].pretty_print()

from typing import Literal

from langchain_deepseek import ChatDeepSeek
from langchain_core.tools import tool

from langgraph.checkpoint.memory import MemorySaver
from langgraph.graph import MessagesState, StateGraph, START, END
from langgraph.prebuilt import ToolNode

# 注意：使用内存存储来存储记忆
memory = MemorySaver()


@tool
def search(query: str):
    """调用此函数可以浏览网络。"""
    # 模拟一个网络搜索返回
    return "北京天气晴朗 大约22度 湿度30%"


tools = [search]
tool_node = ToolNode(tools)

model = ChatOpenAI(
    model="Pro/deepseek-ai/DeepSeek-V3",
    temperature=0,
    api_key=os.environ.get("DEEPSEEK_API_KEY"),
    base_url=os.environ.get("DEEPSEEK_API_BASE"),
)


# model = ChatDeepSeek(
#     model="Pro/deepseek-ai/DeepSeek-V3",
#     temperature=0,
#     api_key=os.environ.get("DEEPSEEK_API_KEY"),
#     base_url=os.environ.get("DEEPSEEK_API_BASE"),
# )


bound_model = model.bind_tools(tools) #绑定


def should_continue(state: MessagesState):
    """返回下一个要执行的节点。"""
    last_message = state["messages"][-1]
    # 如果没有函数调用，则结束
    if not last_message.tool_calls:
        return END
    # 否则如果有，我们继续
    return "action"


# 定义调用模型的函数
def call_model(state: MessagesState):
    response = bound_model.invoke(state["messages"])
    # 我们返回一个列表，因为这会被添加到现有列表中
    return {"messages": response}


# 定义一个图
workflow = StateGraph(MessagesState)

# 定义我们将在其间循环的两个节点
workflow.add_node("agent", call_model)
workflow.add_node("action", tool_node)

# 将入口点设置为 `agent`
# 这意味着这个节点是第一个被调用的
workflow.add_edge(START, "agent")

# 现在我们添加一个条件边
workflow.add_conditional_edges(
    # 首先，我们定义起始节点。我们使用 `agent`。
    # 这意味着这些是在 `agent` 节点被调用后采取的边。
    "agent",
    # 接下来，我们传入将确定下一个调用哪个节点的函数。
    should_continue,
    # 接下来，我们传入路径映射 - 这条边可能去往的所有可能节点
    ["action", END],
)

# 现在我们从 `tools` 到 `agent` 添加一个普通边。
# 这意味着在调用 `tools` 之后，接下来调用 `agent` 节点。
workflow.add_edge("action", "agent")

# 最后，我们编译它！
# 这将它编译成一个 LangChain Runnable，
# 意味着你可以像使用任何其他 runnable 一样使用它
# 设置检查点为内存形式，注意没有设置store
app = workflow.compile(checkpointer=memory)


from langchain_core.messages import HumanMessage

config = {"configurable": {"thread_id": "20"}}
input_message = HumanMessage(content="hi! 我是tomie")
for event in app.stream({"messages": [input_message]}, config, stream_mode="values"):
    event["messages"][-1].pretty_print()


input_message = HumanMessage(content="我叫什么名字?")
for event in app.stream({"messages": [input_message]}, config, stream_mode="values"):
    event["messages"][-1].pretty_print()

# 安装后，启动MongoDB服务
# 通常安装后会自动设置为系统服务并启动
# 如果没有自动启动，可以在命令提示符中运行：
net start MongoDB

# 安装
brew tap mongodb/brew
brew install mongodb-community

# 启动服务
brew services start mongodb-community


# 拉取MongoDB镜像
docker pull mongo

# 运行MongoDB容器
docker run -d -p 27017:27017 --name mongodb mongo

# 验证容器是否运行
docker ps


! pip install -U pymongo langgraph langgraph-checkpoint-mongodb

import pymongo

# 创建MongoDB客户端连接
client = pymongo.MongoClient("mongodb://localhost:27017/")

# 测试连接
try:
    client.admin.command('ping')
    print("MongoDB连接成功！")
except Exception as e:
    print(f"MongoDB连接失败: {e}")


from typing import Literal

from langchain_core.tools import tool
from langchain_deepseek import ChatDeepSeek
from langgraph.prebuilt import create_react_agent


@tool
def get_weather(city: Literal["北京", "深圳"]):
    """用来返回天气信息的工具函数。"""
    if city == "北京":
        return "北京天气晴朗 大约22度 湿度30%"
    elif city == "深圳":
        return "深圳天气多云 大约28度 湿度80%"
    else:
        raise AssertionError("Unknown city")


tools = [get_weather]
# model = ChatDeepSeek(
#     model="Pro/deepseek-ai/DeepSeek-V3",
#     temperature=0,
#     api_key=os.environ.get("DEEPSEEK_API_KEY"),
#     base_url=os.environ.get("DEEPSEEK_API_BASE"),
# )

model = ChatOpenAI(
    model="deepseek-ai/DeepSeek-V3",
    temperature=0,
    api_key=os.getenv("DEEPSEEK_API_KEY"),
    base_url=os.getenv("DEEPSEEK_API_BASE"),)

from langgraph.checkpoint.mongodb import MongoDBSaver

MONGODB_URI = "localhost:27017"  # replace this with your connection string

with MongoDBSaver.from_conn_string(MONGODB_URI) as checkpointer:
    graph = create_react_agent(model, tools=tools, checkpointer=checkpointer)
    config = {"configurable": {"thread_id": "1"}}
    response = graph.invoke(
        {"messages": [("human", "北京今天的天气如何？")]}, config
    )

print(response)

from typing import Literal

from langchain_deepseek import ChatDeepSeek
from langchain_core.tools import tool

from langgraph.checkpoint.memory import MemorySaver
from langgraph.graph import MessagesState, StateGraph, START
from langgraph.prebuilt import ToolNode

memory = MemorySaver()


@tool
def search(query: str):
    """调用此函数可以浏览网络。"""
    # 模拟一个网络搜索返回
    return "北京天气晴朗 大约22度 湿度30%"


tools = [search]
tool_node = ToolNode(tools)
# model = ChatDeepSeek(
#     model="Pro/deepseek-ai/DeepSeek-V3",
#     temperature=0,
#     api_key=os.environ.get("DEEPSEEK_API_KEY"),
#     base_url=os.environ.get("DEEPSEEK_API_BASE"),
# )
bound_model = model.bind_tools(tools)


def should_continue(state: MessagesState):
    """返回下一个要执行的节点。"""
    last_message = state["messages"][-1]
    # 如果没有函数调用，则结束
    if not last_message.tool_calls:
        return END
    # 否则，如果有函数调用，我们继续
    return "action"


def filter_messages(messages: list):
    # 这是一个非常简单的辅助函数，它只使用最后一条消息
    return messages[-3:]


# 定义调用模型的函数
def call_model(state: MessagesState):
    messages = filter_messages(state["messages"])
    response = bound_model.invoke(messages)
    # 我们返回一个列表，因为这将被添加到现有列表中
    return {"messages": response}


# 定义一个新图
workflow = StateGraph(MessagesState)

# 定义我们将在其间循环的两个节点
workflow.add_node("agent", call_model)
workflow.add_node("action", tool_node)

# 将入口点设置为 `agent`
# 这意味着这个节点是第一个被调用的
workflow.add_edge(START, "agent")

# 现在添加一个条件边
workflow.add_conditional_edges(
    # 首先，我们定义起始节点。我们使用 `agent`。
    # 这意味着这些是在调用 `agent` 节点后采取的边。
    "agent",
    # 接下来，我们传入将确定下一个调用哪个节点的函数。
    should_continue,
    # 接下来，我们传入路径图 - 此边可能去往的所有可能节点
    ["action", END],
)

# 现在我们从 `action` 到 `agent` 添加一个普通边。
# 这意味着在调用 `action` 之后，下一步调用 `agent` 节点。
workflow.add_edge("action", "agent")

# 最后，我们编译它！
# 这将它编译成一个 LangChain Runnable，
# 意味着你可以像使用任何其他 runnable 一样使用它
app = workflow.compile(checkpointer=memory)

from langchain_core.messages import HumanMessage

config = {"configurable": {"thread_id": "2"}}
input_message = HumanMessage(content="hi! 我是tomie")
for event in app.stream({"messages": [input_message]}, config, stream_mode="values"):
    event["messages"][-1].pretty_print()

# 请注意，我们在这里使用了一个辅助函数，它只使用最后一条消息
# 这将导致我们的模型只看到最后一条消息
input_message = HumanMessage(content="我叫什么名字?")
for event in app.stream({"messages": [input_message]}, config, stream_mode="values"):
    event["messages"][-1].pretty_print()

from typing import Literal

from langchain_deepseek import ChatDeepSeek
from langchain_core.messages import SystemMessage, RemoveMessage, HumanMessage
from langgraph.checkpoint.memory import MemorySaver
from langgraph.graph import MessagesState, StateGraph, START, END

memory = MemorySaver()


# 我们将添加一个`summary`属性（除了MessagesState已有的`messages`键之外）
class State(MessagesState):
    summary: str


# 我们将使用这个模型进行对话和总结
# model = ChatDeepSeek(
#     model="Pro/deepseek-ai/DeepSeek-V3",
#     temperature=0,
#     api_key=os.environ.get("DEEPSEEK_API_KEY"),
#     base_url=os.environ.get("DEEPSEEK_API_BASE"),
# )


# 定义调用模型的逻辑
def call_model(state: State):
    # 如果存在摘要，我们将其作为系统消息添加
    summary = state.get("summary", "")
    if summary:
        system_message = f"之前对话的摘要: {summary}"
        messages = [SystemMessage(content=system_message)] + state["messages"]
    else:
        messages = state["messages"]
    response = model.invoke(messages)
    # 我们返回一个列表，因为这将被添加到现有列表中
    return {"messages": [response]}


# 现在我们定义确定是结束还是总结对话的逻辑
def should_continue(state: State) -> Literal["summarize_conversation", END]:
    """返回下一个要执行的节点。"""
    messages = state["messages"]
    # 如果消息超过六条，则我们总结对话
    if len(messages) > 6:
        return "summarize_conversation"
    # 否则我们可以直接结束
    return END


def summarize_conversation(state: State):
    # 首先，我们总结对话
    summary = state.get("summary", "")
    if summary:
        # 如果已经存在摘要，我们使用不同的系统提示来总结它
        # 与没有摘要的情况不同
        summary_message = (
            f"这是迄今为止对话的摘要: {summary}\n\n"
            "考虑上面的新消息，扩展摘要:"
        )
    else:
        summary_message = "创建上述对话的摘要:"

    messages = state["messages"] + [HumanMessage(content=summary_message)]
    response = model.invoke(messages)
    # 现在我们需要删除我们不再想显示的消息
    # 我将删除除最后两条以外的所有消息，但你可以更改这一点
    delete_messages = [RemoveMessage(id=m.id) for m in state["messages"][:-2]]
    return {"summary": response.content, "messages": delete_messages}


# 定义一个新图
workflow = StateGraph(State)

# 定义对话节点和总结节点
workflow.add_node("conversation", call_model)
workflow.add_node(summarize_conversation)

# 将入口点设置为对话
workflow.add_edge(START, "conversation")

# 现在添加一个条件边
workflow.add_conditional_edges(
    # 首先，我们定义起始节点。我们使用`conversation`。
    # 这意味着这些是在调用`conversation`节点后采取的边。
    "conversation",
    # 接下来，我们传入将确定下一个调用哪个节点的函数。
    should_continue,
)

# 现在我们从`summarize_conversation`到END添加一个普通边。
# 这意味着在调用`summarize_conversation`之后，我们结束。
workflow.add_edge("summarize_conversation", END)

# 最后，我们编译它！
app = workflow.compile(checkpointer=memory)


def print_update(update):
    for k, v in update.items():
        for m in v["messages"]:
            m.pretty_print()
        if "summary" in v:
            print(v["summary"])

from langchain_core.messages import HumanMessage

config = {"configurable": {"thread_id": "4"}}
input_message = HumanMessage(content="hi! 我是tomie")
input_message.pretty_print()
for event in app.stream({"messages": [input_message]}, config, stream_mode="updates"):
    print_update(event)

input_message = HumanMessage(content="我叫什么名字?")
input_message.pretty_print()
for event in app.stream({"messages": [input_message]}, config, stream_mode="updates"):
    print_update(event)

input_message = HumanMessage(content="我喜欢AI应用开发!")
input_message.pretty_print()
for event in app.stream({"messages": [input_message]}, config, stream_mode="updates"):
    print_update(event)

values = app.get_state(config).values
values

input_message = HumanMessage(content="我更喜欢Python!")
input_message.pretty_print()
for event in app.stream({"messages": [input_message]}, config, stream_mode="updates"):
    print_update(event)

values = app.get_state(config).values
values

input_message = HumanMessage(content="我叫什么名字?")
input_message.pretty_print()
for event in app.stream({"messages": [input_message]}, config, stream_mode="updates"):
    print_update(event)